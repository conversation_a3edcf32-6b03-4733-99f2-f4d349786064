# Qwen API发音纠正指导生成器使用指南

## 🎯 功能概述

这个生成器使用Qwen API来生成真正的AI驱动的发音纠正指导，基于您提供的五步纠正法：

1. **Say the word aloud** (大声说出这个词)
2. **Decide if you know it** (判断是否理解)  
3. **Think of words that sound similar** (想想发音相似的词)
4. **Choose the closest-sounding real word** (选择发音最接近的真实词汇)
5. **Check if it makes sense in context** (检查在语境中是否合理)

## 🚀 快速开始

### 1. 设置API密钥

#### 方法一：环境变量（推荐）
```bash
export QWEN_API_KEY="your_actual_api_key_here"
```

#### 方法二：直接修改代码
在 `scripts/data_generation/qwen_pronunciation_generator.py` 中：
```python
API_KEY = "your_actual_api_key_here"  # 替换为您的实际密钥
```

### 2. 安装依赖
```bash
pip install requests
```

### 3. 运行生成器
```bash
cd /path/to/your/project
python3 scripts/data_generation/qwen_pronunciation_generator.py
```

## 📊 生成的数据结构

### 输入数据（预设的发音错误）
```json
{
  "12_months": {
    "words": ["妈妈", "爸爸", "水水", "抱抱"],
    "common_errors": {
      "妈妈": ["na na", "ma ma(平声)", "ba ba"],
      "水水": ["dui dui", "sui sui", "tu tu"]
    }
  }
}
```

### 输出数据（LLM生成的指导）
```json
{
  "id": "qwen_abc123def4",
  "type": "llm_generated_case",
  "source": {
    "generator": "Qwen API",
    "generation_date": "2024-01-15 14:30:25",
    "credibility_score": 8,
    "method": "五步发音纠正法"
  },
  "case_info": {
    "child_age_months": 18,
    "target_word": "水水",
    "mispronunciation": "dui dui",
    "correct_pronunciation": "水水",
    "age_group": "18_months"
  },
  "llm_guidance": {
    "raw_response": "LLM生成的完整指导内容...",
    "prompt_used": "使用的完整prompt..."
  }
}
```

## 🔧 自定义配置

### 修改生成参数
在 `config/qwen_config.py` 中调整：

```python
# 模型参数
MODEL_PARAMS = {
    "model": "qwen-turbo",    # 可选: qwen-turbo, qwen-plus, qwen-max
    "temperature": 0.7,       # 创造性控制 (0-1)
    "max_tokens": 2000,       # 最大输出长度
    "top_p": 0.9             # 核采样参数
}

# 生成参数
GENERATION_PARAMS = {
    "max_retries": 3,         # 最大重试次数
    "rate_limit_delay": 1     # API调用间隔（秒）
}
```

### 添加新的发音错误模式
在 `qwen_pronunciation_generator.py` 中的 `pronunciation_data` 添加：

```python
"新年龄段": {
    "words": ["新词汇1", "新词汇2"],
    "common_errors": {
        "新词汇1": ["错误发音1", "错误发音2"],
        "新词汇2": ["错误发音3", "错误发音4"]
    }
}
```

## 📝 Prompt模板说明

### 当前使用的Prompt
```python
five_step_prompt = """
你是一位专业的儿童语言治疗师，请基于以下五步发音纠正法为家长提供具体的指导建议：

五步纠正法：
1. Say the word aloud (大声说出这个词) - 家长清晰示范正确发音
2. Decide if you know it (判断是否理解) - 确认孩子理解词汇含义  
3. Think of words that sound similar (想想发音相似的词) - 引导联想相似发音
4. Choose the closest-sounding real word (选择发音最接近的真实词汇) - 从简单开始练习
5. Check if it makes sense in context (检查在语境中是否合理) - 在实际情境中验证

核心原则：分步矫正 + 语境检验 + 鼓励语

现在请为以下情况提供详细的纠正指导：

孩子信息：
- 年龄：{age}个月
- 目标词汇：{target_word}
- 错误发音：{mispronunciation}
- 正确发音：{correct_pronunciation}

请提供：
1. 问题分析（为什么会出现这种错误）
2. 五步纠正法的具体实施步骤
3. 3-4个具体的鼓励语句
4. 预期改善时间
5. 注意事项

请用温和、专业、实用的语调回答，确保家长容易理解和执行。
"""
```

### 自定义Prompt
您可以根据需要修改prompt，例如：
- 添加更多专业术语解释
- 增加安全注意事项
- 调整语言风格
- 添加特定的训练方法

## 🎮 使用示例

### 基本使用
```python
from scripts.data_generation.qwen_pronunciation_generator import QwenPronunciationGenerator

# 创建生成器
generator = QwenPronunciationGenerator("your_api_key")

# 生成单个案例
case = generator.generate_pronunciation_case(age_months=18)

# 批量生成
cases = generator.generate_batch_cases(
    num_cases=10,
    age_range=(12, 36)
)

# 保存数据
generator.save_generated_data(cases, "my_generated_cases.json")
```

### 高级使用
```python
# 自定义年龄范围和数量
cases = generator.generate_batch_cases(
    num_cases=20,
    age_range=(15, 30)  # 只生成15-30个月的案例
)

# 针对特定年龄生成
specific_cases = []
for age in [12, 18, 24, 30, 36]:
    case = generator.generate_pronunciation_case(age)
    if case:
        specific_cases.append(case)
```

## 📊 生成质量控制

### API调用监控
生成器会自动：
- 重试失败的API调用（最多3次）
- 控制调用频率避免超限
- 记录生成成功率

### 数据质量检查
- 验证API响应格式
- 检查生成内容完整性
- 记录生成时间和参数

## 🔍 故障排除

### 常见问题

#### 1. API密钥错误
```
错误: 未设置QWEN_API_KEY环境变量
```
**解决**: 设置正确的环境变量或在代码中配置API密钥

#### 2. API调用失败
```
API调用失败，状态码: 401
```
**解决**: 检查API密钥是否有效，是否有足够的配额

#### 3. 生成内容为空
```
生成失败: 水水
```
**解决**: 检查网络连接，调整prompt复杂度，或增加重试次数

#### 4. 生成速度慢
**解决**: 
- 减少batch_size
- 增加rate_limit_delay
- 使用更快的模型（如qwen-turbo）

### 调试模式
在代码中添加调试信息：
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看API请求详情
print(f"使用的prompt: {prompt}")
print(f"API响应: {response.text}")
```

## 💡 最佳实践

### 1. 批量生成建议
- 首次使用建议生成5-10个案例测试
- 确认质量后再进行大批量生成
- 定期保存中间结果避免数据丢失

### 2. 成本控制
- 使用qwen-turbo模型降低成本
- 合理设置max_tokens避免过长输出
- 监控API使用量

### 3. 质量优化
- 定期review生成的内容质量
- 根据实际需求调整prompt
- 收集用户反馈持续改进

## 📈 扩展建议

### 1. 添加更多年龄段
- 扩展到3-6岁儿童
- 添加特殊需求儿童的案例

### 2. 增加发音类型
- 方言发音纠正
- 双语环境发音问题
- 特殊音素训练

### 3. 集成其他功能
- 语音识别评估
- 进度跟踪系统
- 个性化推荐

---

**注意**: 请确保遵守Qwen API的使用条款和限制，合理使用API配额。生成的内容仅供参考，严重的语言发育问题请咨询专业医师。
