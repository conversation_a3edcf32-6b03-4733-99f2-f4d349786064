#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将婴幼儿发音纠正数据集转换为微调训练格式
Convert infant pronunciation correction dataset to fine-tuning format
"""

import json
import os
from typing import List, Dict, Any

def convert_to_instruction_format(input_file: str, output_file: str):
    """
    将原始数据集转换为 instruction-input-output 格式
    """
    
    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取案例数据
    if 'cases' in data:
        cases = data['cases']
    elif 'data' in data:
        cases = data['data']
    elif isinstance(data, list):
        cases = data
    else:
        print(f"❌ 无法识别数据格式: {input_file}")
        return
    
    converted_data = []
    
    for case in cases:
        try:
            # 提取基本信息
            case_info = case.get('case_info', {})
            llm_guidance = case.get('llm_guidance', {})
            
            age = case_info.get('child_age_months', 'N/A')
            target_word = case_info.get('target_word', 'N/A')
            mispronunciation = case_info.get('mispronunciation', 'N/A')
            correct_pronunciation = case_info.get('correct_pronunciation', target_word)
            
            # 构建 instruction
            instruction = """你是一位专业的儿童语言治疗师，请基于五步发音纠正法为家长提供具体的指导建议。

五步纠正法：
1. Say the word aloud (大声说出这个词) - 家长清晰示范正确发音
2. Decide if you know it (判断是否理解) - 确认孩子理解词汇含义  
3. Think of words that sound similar (想想发音相似的词) - 引导联想相似发音
4. Choose the closest-sounding real word (选择发音最接近的真实词汇) - 从简单开始练习
5. Check if it makes sense in context (检查在语境中是否合理) - 在实际情境中验证

核心原则：分步矫正 + 语境检验 + 鼓励语

请提供：
1. 问题分析（为什么会出现这种错误）
2. 五步纠正法的具体实施步骤
3. 3-4个具体的鼓励语句
4. 预期改善时间
5. 注意事项

请用温和、专业、实用的语调回答，确保家长容易理解和执行。"""
            
            # 构建 input
            input_text = f"""孩子信息：
- 年龄：{age}个月
- 目标词汇：{target_word}
- 错误发音：{mispronunciation}
- 正确发音：{correct_pronunciation}"""
            
            # 获取 output
            output_text = llm_guidance.get('raw_response', '')
            
            # 如果没有raw_response，尝试其他字段
            if not output_text:
                step_by_step = llm_guidance.get('step_by_step_correction', '')
                context_verification = llm_guidance.get('context_verification', '')
                encouragement = llm_guidance.get('encouragement', '')
                
                if step_by_step or context_verification or encouragement:
                    output_text = f"""## 发音纠正指导

### 分步矫正方法
{step_by_step}

### 语境检验
{context_verification}

### 鼓励语
{encouragement}"""
            
            # 确保有有效的输出
            if not output_text or len(output_text.strip()) < 50:
                print(f"⚠️  跳过无效案例: {case.get('id', 'unknown')}")
                continue
            
            # 创建训练样本
            training_sample = {
                "instruction": instruction,
                "input": input_text,
                "output": output_text,
                "metadata": {
                    "original_id": case.get('id', ''),
                    "age_months": age,
                    "target_word": target_word,
                    "error_type": case_info.get('error_type', ''),
                    "credibility_score": case.get('source', {}).get('credibility_score', 0),
                    "generation_date": case.get('source', {}).get('generation_date', '')
                }
            }
            
            converted_data.append(training_sample)
            
        except Exception as e:
            print(f"❌ 处理案例时出错: {case.get('id', 'unknown')} - {str(e)}")
            continue
    
    # 保存转换后的数据
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(converted_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 转换完成: {len(converted_data)} 条训练样本")
    print(f"📁 输出文件: {output_file}")
    
    return converted_data

def convert_all_datasets():
    """转换所有数据集文件"""
    
    # 数据集文件列表
    dataset_files = [
        'qwen_generated_pronunciation_cases.json',
        'generated_cases_final_200.json',
        'final_0_3_pronunciation_dataset.json',
        'collected_real_data.json',
        'pronunciation_correction_0_3_years.json'
    ]
    
    all_training_data = []
    
    for file_name in dataset_files:
        input_path = file_name
        output_path = f"training_format_{file_name}"
        
        if os.path.exists(input_path):
            print(f"\n🔄 转换文件: {file_name}")
            converted_data = convert_to_instruction_format(input_path, output_path)
            if converted_data:
                all_training_data.extend(converted_data)
        else:
            print(f"⚠️  文件不存在: {file_name}")
    
    # 合并所有数据
    if all_training_data:
        # 按质量分数排序
        all_training_data.sort(key=lambda x: x['metadata']['credibility_score'], reverse=True)
        
        # 保存合并后的训练数据
        with open('merged_training_data.json', 'w', encoding='utf-8') as f:
            json.dump(all_training_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎯 合并完成:")
        print(f"📊 总训练样本: {len(all_training_data)}")
        print(f"📁 合并文件: merged_training_data.json")
        
        # 统计信息
        quality_stats = {}
        age_stats = {}
        
        for sample in all_training_data:
            score = sample['metadata']['credibility_score']
            age = sample['metadata']['age_months']
            
            quality_stats[score] = quality_stats.get(score, 0) + 1
            age_stats[age] = age_stats.get(age, 0) + 1
        
        print(f"\n📈 质量分布:")
        for score in sorted(quality_stats.keys(), reverse=True):
            print(f"  {score}分: {quality_stats[score]}条")
        
        print(f"\n📅 年龄分布 (前10):")
        for age in sorted(age_stats.keys())[:10]:
            print(f"  {age}个月: {age_stats[age]}条")
    
    return all_training_data

def create_sample_preview(data: List[Dict], num_samples: int = 3):
    """创建样本预览"""
    
    if not data:
        print("❌ 没有数据可预览")
        return
    
    print(f"\n📝 样本预览 (前{num_samples}条):")
    print("=" * 80)
    
    for i, sample in enumerate(data[:num_samples]):
        print(f"\n样本 {i+1}:")
        print(f"ID: {sample['metadata']['original_id']}")
        print(f"年龄: {sample['metadata']['age_months']}个月")
        print(f"目标词: {sample['metadata']['target_word']}")
        print(f"质量分数: {sample['metadata']['credibility_score']}")
        
        print(f"\nINSTRUCTION (前200字符):")
        print(sample['instruction'][:200] + "...")
        
        print(f"\nINPUT:")
        print(sample['input'])
        
        print(f"\nOUTPUT (前300字符):")
        print(sample['output'][:300] + "...")
        
        print("-" * 80)

def main():
    """主函数"""
    print("🚀 婴幼儿发音纠正数据集格式转换")
    print("=" * 50)
    
    # 转换所有数据集
    training_data = convert_all_datasets()
    
    # 创建样本预览
    create_sample_preview(training_data, num_samples=2)
    
    # 创建不同格式的输出
    if training_data:
        # 创建简化版本 (只包含核心字段)
        simplified_data = []
        for sample in training_data:
            simplified_sample = {
                "instruction": sample["instruction"],
                "input": sample["input"], 
                "output": sample["output"]
            }
            simplified_data.append(simplified_sample)
        
        with open('simplified_training_data.json', 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 创建简化版本: simplified_training_data.json")
        print(f"📊 简化版本样本数: {len(simplified_data)}")
        
        # 创建高质量子集 (credibility_score >= 8)
        high_quality_data = [
            sample for sample in training_data 
            if sample['metadata']['credibility_score'] >= 8
        ]
        
        if high_quality_data:
            high_quality_simplified = [
                {
                    "instruction": sample["instruction"],
                    "input": sample["input"],
                    "output": sample["output"]
                }
                for sample in high_quality_data
            ]
            
            with open('high_quality_training_data.json', 'w', encoding='utf-8') as f:
                json.dump(high_quality_simplified, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 创建高质量版本: high_quality_training_data.json")
            print(f"📊 高质量版本样本数: {len(high_quality_simplified)}")

if __name__ == "__main__":
    main()
