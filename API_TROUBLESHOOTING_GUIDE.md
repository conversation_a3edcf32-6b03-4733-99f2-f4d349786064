# Qwen API调用异常问题解决指南

## 🔍 问题诊断结果

根据API诊断工具的测试结果，您的API连接状态良好：

### ✅ 诊断通过项目
- **基础连接**: ✅ 正常
- **模型可用性**: ✅ 所有模型可用 (qwen-turbo, qwen-plus, qwen-max, qwen-plus-2025-04-28)
- **频率限制**: ✅ 无限制 (10/10请求成功)
- **大型Prompt**: ✅ 处理正常
- **网络稳定性**: ✅ 100%成功率，平均响应0.72秒

## 🛠️ 已实施的改进措施

### 1. 增强的错误处理机制

#### 详细的状态码处理
```python
- 200: 成功处理
- 400: 请求参数错误 (不重试)
- 401: API密钥无效 (不重试)
- 429: 频率限制 (延长等待时间)
- 500: 服务器错误 (可重试)
- 503: 服务不可用 (延长等待时间)
```

#### 智能重试策略
- **重试次数**: 从3次增加到5次
- **等待策略**: 指数退避，最大30秒
- **特殊处理**: 429和503错误使用更长等待时间

### 2. 优化的API调用参数

#### 基于诊断结果的推荐配置
```python
{
    "model": "qwen-plus-2025-04-28",  # 您指定的模型
    "max_tokens": 1500,               # 减少token避免超限
    "timeout": 60,                    # 增加超时时间
    "temperature": 0.7,               # 保持创造性
    "top_p": 0.9                      # 保持质量
}
```

#### Prompt优化
- **长度检查**: 超过8000字符自动截断
- **内容验证**: 确保返回内容有意义(>50字符)
- **格式验证**: 检查JSON响应格式

### 3. 批量生成的改进

#### 进度保存和恢复
- **自动保存**: 每5个案例自动保存进度
- **断点恢复**: 支持从中断点继续生成
- **失败容错**: 允许50%的失败率

#### 动态调整策略
- **等待时间**: 根据失败次数动态调整
- **成功率监控**: 实时显示生成成功率
- **智能停止**: 失败过多时自动停止

## 🚀 使用建议

### 1. 最佳实践

#### 批量生成建议
```python
# 推荐的批量生成参数
cases = generator.generate_batch_cases(
    num_cases=50,                    # 建议先小批量测试
    age_range=(12, 36),
    save_interval=5,                 # 每5个保存一次
    resume_file="data/progress.json" # 支持断点恢复
)
```

#### 错误处理建议
- 遇到429错误时，等待时间会自动延长
- 遇到网络错误时，会自动重试
- 保存进度文件，避免重复生成

### 2. 监控和调试

#### 实时监控
- 每次API调用都有详细的状态输出
- 成功率和失败次数实时统计
- 响应时间监控

#### 调试工具
```bash
# 运行API诊断工具
python3 scripts/diagnostics/api_diagnostics.py

# 查看详细的API调用日志
# 所有调用都有emoji标识和详细信息
```

## 🔧 常见问题解决方案

### 问题1: API调用超时
**症状**: 请求超时，连接中断
**解决方案**:
- ✅ 已增加超时时间到60秒
- ✅ 已添加网络异常重试机制
- ✅ 已实施指数退避策略

### 问题2: 频率限制
**症状**: 429状态码，请求过于频繁
**解决方案**:
- ✅ 已添加429错误的特殊处理
- ✅ 动态调整等待时间(最长60秒)
- ✅ 基础等待时间设为2秒

### 问题3: 响应格式异常
**症状**: API返回格式不正确
**解决方案**:
- ✅ 已添加JSON解析异常处理
- ✅ 验证响应内容完整性
- ✅ 检查返回内容长度

### 问题4: 批量生成中断
**症状**: 生成过程中断，进度丢失
**解决方案**:
- ✅ 已实现自动进度保存
- ✅ 支持断点恢复功能
- ✅ 失败容错机制

## 📊 性能优化

### 当前性能指标
- **API响应时间**: 平均0.72秒
- **成功率**: 100% (诊断测试)
- **网络稳定性**: 优秀
- **模型可用性**: 全部可用

### 优化建议
1. **批量大小**: 建议50-100个案例为一批
2. **并发控制**: 当前串行处理，避免频率限制
3. **缓存策略**: 相同输入会产生不同输出，无需缓存
4. **错误恢复**: 自动重试和进度保存

## 🎯 实际使用示例

### 小批量测试
```bash
# 先测试5个案例
python3 test_qwen_generator.py
```

### 中等批量生成
```python
# 生成50个案例，每10个保存一次
cases = generator.generate_batch_cases(
    num_cases=50,
    save_interval=10
)
```

### 大批量生成
```python
# 生成200个案例，支持断点恢复
cases = generator.generate_batch_cases(
    num_cases=200,
    save_interval=5,
    resume_file="data/large_batch_progress.json"
)
```

## 📋 故障排除检查清单

### 在遇到问题时，请按顺序检查：

1. **API密钥**: ✅ 已验证有效
2. **网络连接**: ✅ 已验证稳定
3. **模型可用性**: ✅ 已验证可用
4. **频率限制**: ✅ 已验证无限制
5. **Prompt格式**: ✅ 已优化处理
6. **错误重试**: ✅ 已实现智能重试
7. **进度保存**: ✅ 已实现自动保存

### 如果仍有问题：

1. **运行诊断工具**:
   ```bash
   python3 scripts/diagnostics/api_diagnostics.py
   ```

2. **检查错误日志**: 查看详细的错误信息和状态码

3. **调整参数**: 根据错误类型调整重试次数和等待时间

4. **分批处理**: 将大批量任务分解为小批量

## 🎉 总结

基于诊断结果，您的API环境完全正常。我们已经实施了全面的改进措施：

- ✅ **增强错误处理**: 5次重试 + 智能等待策略
- ✅ **进度保存**: 自动保存 + 断点恢复
- ✅ **实时监控**: 详细状态输出 + 成功率统计
- ✅ **参数优化**: 基于诊断结果的最佳配置
- ✅ **容错机制**: 50%失败容忍度 + 智能停止

现在您可以放心地进行大批量数据生成，系统会自动处理各种异常情况并保证数据不丢失！
