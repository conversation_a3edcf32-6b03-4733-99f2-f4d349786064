#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿发音纠正数据集验证器
用于验证数据格式、完整性和质量
"""

import json
from typing import Dict, List, Any, Tuple
import re
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 简化版JSON Schema验证（不依赖jsonschema库）
class SimpleValidator:
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """验证必需字段"""
        errors = []
        for field in required_fields:
            if field not in data:
                errors.append(f"缺少必需字段: {field}")
        return errors

    @staticmethod
    def validate_field_type(data: Dict[str, Any], field: str, expected_type: type) -> List[str]:
        """验证字段类型"""
        errors = []
        if field in data and not isinstance(data[field], expected_type):
            errors.append(f"字段 {field} 类型错误，期望 {expected_type.__name__}")
        return errors

class DataValidator:
    """数据验证器类"""
    
    def __init__(self):
        self.schema = self._load_schema()
        self.validation_results = []
        
    def _load_schema(self) -> Dict[str, Any]:
        """加载JSON Schema验证规则"""
        return {
            "type": "object",
            "required": ["id", "type", "source", "metadata"],
            "properties": {
                "id": {"type": "string", "pattern": "^[a-zA-Z0-9_]+$"},
                "type": {
                    "type": "string",
                    "enum": ["official_standard", "professional_literature", "qa_pair"]
                },
                "source": {
                    "type": "object",
                    "required": ["credibility_score"],
                    "properties": {
                        "credibility_score": {"type": "number", "minimum": 1, "maximum": 10}
                    }
                },
                "metadata": {
                    "type": "object",
                    "required": ["last_updated"],
                    "properties": {
                        "last_updated": {"type": "string", "format": "date"}
                    }
                }
            }
        }
    
    def validate_data_structure(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证数据结构"""
        errors = []

        # 使用简化验证器
        validator = SimpleValidator()

        # 验证必需字段
        required_fields = ["id", "type", "source", "metadata"]
        errors.extend(validator.validate_required_fields(data, required_fields))

        # 验证字段类型
        errors.extend(validator.validate_field_type(data, "id", str))
        errors.extend(validator.validate_field_type(data, "type", str))
        errors.extend(validator.validate_field_type(data, "source", dict))
        errors.extend(validator.validate_field_type(data, "metadata", dict))

        # 验证type字段值
        if "type" in data:
            valid_types = ["official_standard", "professional_literature", "qa_pair"]
            if data["type"] not in valid_types:
                errors.append(f"无效的数据类型: {data['type']}")

        # 验证source中的可信度评分
        if "source" in data and isinstance(data["source"], dict):
            source = data["source"]
            if "credibility_score" in source:
                score = source["credibility_score"]
                if not isinstance(score, (int, float)) or score < 1 or score > 10:
                    errors.append("可信度评分必须在1-10之间")

        return len(errors) == 0, errors
    
    def validate_age_range(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证年龄范围合理性"""
        errors = []
        
        # 检查年龄范围
        if "content" in data:
            content = data["content"]
            
            # 检查age_range字段
            if "age_range" in content:
                age_range = content["age_range"]
                min_months = age_range.get("min_months", 0)
                max_months = age_range.get("max_months", 0)
                
                if min_months < 0 or max_months > 72:
                    errors.append("年龄范围超出0-72个月范围")
                    
                if min_months >= max_months:
                    errors.append("最小年龄应小于最大年龄")
            
            # 检查child_info中的年龄
            if data["type"] == "qa_pair" and "question" in data:
                question = data["question"]
                if "child_info" in question:
                    child_age = question["child_info"].get("age_months", 0)
                    if child_age < 0 or child_age > 72:
                        errors.append("儿童年龄超出合理范围")
        
        return len(errors) == 0, errors
    
    def validate_phoneme_notation(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证音素标记规范性"""
        errors = []
        
        # IPA音素正则表达式（简化版）
        ipa_pattern = r'^/[a-zA-Zɑɒɔəɛɪʊʌæɜɝɞɘɵɤɯɨʉɪʏʊeøɘɵɤɯɨʉoɔɑɒæɛɪʊʌəɜɝɞɘɵɤɯɨʉpbtdkgfvθðszʃʒhmnŋlrjwʔʰʲʷˑːˈˌ]+/$'
        
        def check_phonemes_in_content(content):
            if isinstance(content, dict):
                for key, value in content.items():
                    if key == "phoneme" and isinstance(value, str):
                        if not re.match(ipa_pattern, value):
                            errors.append(f"音素标记不规范: {value}")
                    elif isinstance(value, (dict, list)):
                        check_phonemes_in_content(value)
            elif isinstance(content, list):
                for item in content:
                    check_phonemes_in_content(item)
        
        if "content" in data:
            check_phonemes_in_content(data["content"])
        
        return len(errors) == 0, errors
    
    def validate_credibility_score(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证可信度评分合理性"""
        errors = []
        
        source = data.get("source", {})
        credibility_score = source.get("credibility_score", 0)
        
        # 根据来源类型验证可信度评分
        source_type_scores = {
            "official_standard": (8, 10),  # 官方标准应该有高可信度
            "professional_literature": (7, 10),  # 专业文献
            "qa_pair": (6, 9)  # 问答对
        }
        
        data_type = data.get("type", "")
        if data_type in source_type_scores:
            min_score, max_score = source_type_scores[data_type]
            if credibility_score < min_score:
                errors.append(f"{data_type}类型数据可信度评分过低: {credibility_score}")
        
        return len(errors) == 0, errors
    
    def validate_content_completeness(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证内容完整性"""
        errors = []
        
        data_type = data.get("type", "")
        content = data.get("content", {})
        
        # 根据数据类型检查必需字段
        if data_type == "official_standard":
            required_fields = ["age_range", "developmental_milestones"]
            for field in required_fields:
                if field not in content:
                    errors.append(f"官方标准数据缺少必需字段: {field}")
        
        elif data_type == "professional_literature":
            required_fields = ["topic", "key_findings"]
            for field in required_fields:
                if field not in content:
                    errors.append(f"专业文献数据缺少必需字段: {field}")
        
        elif data_type == "qa_pair":
            if "question" not in data or "answer" not in data:
                errors.append("问答对数据缺少问题或答案")
            else:
                question = data["question"]
                answer = data["answer"]
                
                if "child_info" not in question:
                    errors.append("问答对缺少儿童信息")
                
                if "assessment" not in answer or "recommendations" not in answer:
                    errors.append("问答对答案缺少评估或建议")
        
        return len(errors) == 0, errors
    
    def validate_safety_guidelines(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证安全性指导原则"""
        errors = []
        
        # 检查是否包含不安全的建议
        unsafe_keywords = [
            "强制", "惩罚", "打骂", "威胁",
            "过度训练", "长时间训练", "忽视情绪"
        ]
        
        def check_unsafe_content(content):
            if isinstance(content, str):
                for keyword in unsafe_keywords:
                    if keyword in content:
                        errors.append(f"包含不安全建议关键词: {keyword}")
            elif isinstance(content, dict):
                for value in content.values():
                    check_unsafe_content(value)
            elif isinstance(content, list):
                for item in content:
                    check_unsafe_content(item)
        
        check_unsafe_content(data)
        
        return len(errors) == 0, errors
    
    def validate_single_record(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证单条记录"""
        record_id = data.get("id", "unknown")
        validation_result = {
            "id": record_id,
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 执行各项验证
        validations = [
            ("数据结构", self.validate_data_structure),
            ("年龄范围", self.validate_age_range),
            ("音素标记", self.validate_phoneme_notation),
            ("可信度评分", self.validate_credibility_score),
            ("内容完整性", self.validate_content_completeness),
            ("安全性指导", self.validate_safety_guidelines)
        ]
        
        for validation_name, validation_func in validations:
            try:
                is_valid, errors = validation_func(data)
                if not is_valid:
                    validation_result["valid"] = False
                    validation_result["errors"].extend([f"{validation_name}: {error}" for error in errors])
            except Exception as e:
                validation_result["valid"] = False
                validation_result["errors"].append(f"{validation_name}验证异常: {str(e)}")
        
        return validation_result
    
    def validate_dataset(self, dataset: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证整个数据集"""
        logger.info(f"开始验证数据集，共{len(dataset)}条记录")
        
        results = {
            "total_records": len(dataset),
            "valid_records": 0,
            "invalid_records": 0,
            "validation_details": [],
            "summary": {
                "data_types": {},
                "age_coverage": {"min": 72, "max": 0},
                "credibility_distribution": {}
            }
        }
        
        # 验证每条记录
        for i, record in enumerate(dataset):
            validation_result = self.validate_single_record(record)
            results["validation_details"].append(validation_result)
            
            if validation_result["valid"]:
                results["valid_records"] += 1
            else:
                results["invalid_records"] += 1
                logger.warning(f"记录 {validation_result['id']} 验证失败: {validation_result['errors']}")
            
            # 统计信息
            data_type = record.get("type", "unknown")
            results["summary"]["data_types"][data_type] = results["summary"]["data_types"].get(data_type, 0) + 1
            
            # 年龄覆盖统计
            if "content" in record and "age_range" in record["content"]:
                age_range = record["content"]["age_range"]
                min_age = age_range.get("min_months", 72)
                max_age = age_range.get("max_months", 0)
                results["summary"]["age_coverage"]["min"] = min(results["summary"]["age_coverage"]["min"], min_age)
                results["summary"]["age_coverage"]["max"] = max(results["summary"]["age_coverage"]["max"], max_age)
            
            # 可信度分布统计
            credibility = record.get("source", {}).get("credibility_score", 0)
            credibility_range = f"{int(credibility)}-{int(credibility)+1}"
            results["summary"]["credibility_distribution"][credibility_range] = \
                results["summary"]["credibility_distribution"].get(credibility_range, 0) + 1
        
        # 计算验证通过率
        results["validation_rate"] = results["valid_records"] / results["total_records"] if results["total_records"] > 0 else 0
        
        logger.info(f"验证完成，通过率: {results['validation_rate']:.2%}")
        
        return results

def main():
    """主函数"""
    # 示例用法
    validator = DataValidator()
    
    # 加载示例数据
    try:
        with open("examples/sample_data.json", "r", encoding="utf-8") as f:
            sample_data = json.load(f)
        
        # 验证数据集
        validation_results = validator.validate_dataset(sample_data)
        
        # 输出验证结果
        print(json.dumps(validation_results, ensure_ascii=False, indent=2))
        
    except FileNotFoundError:
        logger.error("示例数据文件未找到")
    except json.JSONDecodeError:
        logger.error("JSON格式错误")
    except Exception as e:
        logger.error(f"验证过程出错: {str(e)}")

if __name__ == "__main__":
    main()
