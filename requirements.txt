# 婴幼儿发音纠正数据集构建项目依赖

# 核心数据处理
pandas>=1.5.0
numpy>=1.21.0
jsonschema>=4.0.0
pyyaml>=6.0

# 网页爬取和数据收集
requests>=2.28.0
beautifulsoup4>=4.11.0
scrapy>=2.6.0
selenium>=4.0.0
lxml>=4.9.0

# PDF文档处理
PyPDF2>=3.0.0
pdfplumber>=0.7.0
pymupdf>=1.20.0

# 文本处理和NLP
jieba>=0.42.1
nltk>=3.7
spacy>=3.4.0
textblob>=0.17.1

# 数据验证和质量控制
cerberus>=1.3.4
great-expectations>=0.15.0
pandera>=0.12.0

# 数据库和存储
sqlalchemy>=1.4.0
pymongo>=4.0.0
redis>=4.3.0

# 机器学习和统计
scikit-learn>=1.1.0
scipy>=1.9.0
statsmodels>=0.13.0

# 可视化和报告
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0
jinja2>=3.1.0

# 日志和监控
loguru>=0.6.0
tqdm>=4.64.0
psutil>=5.9.0

# 配置管理
python-dotenv>=0.20.0
configparser>=5.3.0

# 测试框架
pytest>=7.1.0
pytest-cov>=3.0.0
pytest-mock>=3.8.0

# 代码质量
black>=22.6.0
flake8>=5.0.0
mypy>=0.971
pre-commit>=2.20.0

# 异步处理
asyncio>=3.4.3
aiohttp>=3.8.0
celery>=5.2.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# 加密和安全
cryptography>=37.0.0
hashlib>=20081119

# 网络和HTTP
urllib3>=1.26.0
httpx>=0.23.0

# 文件处理
openpyxl>=3.0.0
xlrd>=2.0.0
python-magic>=0.4.27

# 图像处理（如果需要处理图表）
pillow>=9.2.0
opencv-python>=4.6.0

# 音频处理（如果需要处理语音数据）
librosa>=0.9.0
soundfile>=0.10.0
pydub>=0.25.0

# 开发工具
ipython>=8.4.0
jupyter>=1.0.0
notebook>=6.4.0

# 部署和容器化
gunicorn>=20.1.0
docker>=6.0.0
kubernetes>=24.2.0

# 云服务SDK（根据需要选择）
# boto3>=1.24.0  # AWS
# azure-storage-blob>=12.12.0  # Azure
# google-cloud-storage>=2.5.0  # Google Cloud
# oss2>=2.15.0  # 阿里云OSS

# 数据库驱动
# psycopg2-binary>=2.9.0  # PostgreSQL
# mysql-connector-python>=8.0.0  # MySQL
# cx-Oracle>=8.3.0  # Oracle

# 特定领域工具
# phonemizer>=3.2.0  # 音素转换
# epitran>=1.17  # 音素转录
# panphon>=0.20.0  # 音韵特征

# 可选的性能优化包
# numba>=0.56.0  # JIT编译
# cython>=0.29.0  # C扩展
# pypy>=7.3.0  # 替代Python解释器
